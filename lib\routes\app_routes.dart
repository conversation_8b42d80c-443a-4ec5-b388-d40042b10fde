import 'package:bibl/bnb.dart';
import 'package:bibl/splash.dart';
import 'package:bibl/views/auth_screens/auth.dart';
import 'package:bibl/views/auth_screens/email_register.dart';
import 'package:bibl/views/auth_screens/forgot_password.dart';
import 'package:bibl/views/auth_screens/login.dart';
import 'package:bibl/views/onboarding/onboarding1.dart';
import 'package:bibl/views/onboarding/onboarding2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Premium route management system for the app
///
/// This class defines all routes in a centralized location and provides
/// premium transition animations and optimized durations for professional feel.
class AppRoutes {
  // Route names
  static const String splash = '/splash';
  static const String auth = '/auth';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String onboarding1 = '/onboarding1';
  static const String onboarding2 = '/onboarding2';

  // Premium transition settings for smooth navigation
  static const Transition defaultTransition = Transition.fadeIn;
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 400);

  // Premium curves for smooth animations
  static const Curve defaultCurve = Curves.easeInOutCubic;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeInOut;

  // Get all routes with premium transitions
  static List<GetPage> getPages() {
    return [
      GetPage(
        name: splash,
        page: () => const Splash(),
        transition: Transition.fadeIn,
        transitionDuration: slowDuration,
        curve: slowCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: auth,
        page: () => const Authentication(),
        transition: Transition.fadeIn,
        transitionDuration: normalDuration,
        curve: defaultCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: login,
        page: () => const Login(),
        transition: Transition.rightToLeft,
        transitionDuration: fastDuration,
        curve: fastCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: register,
        page: () => const EmailRegister(),
        transition: Transition.rightToLeft,
        transitionDuration: fastDuration,
        curve: fastCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: forgotPassword,
        page: () => const ForgotPassword(),
        transition: Transition.rightToLeft,
        transitionDuration: fastDuration,
        curve: fastCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: home,
        page: () => const BNB(),
        transition: Transition.fadeIn,
        transitionDuration: normalDuration,
        curve: defaultCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: onboarding1,
        page: () => const OnBoarding1(),
        transition: Transition.fadeIn,
        transitionDuration: normalDuration,
        curve: defaultCurve,
        preventDuplicates: true,
      ),
      GetPage(
        name: onboarding2,
        page: () => const Onboarding2(),
        transition: Transition.rightToLeft,
        transitionDuration: fastDuration,
        curve: fastCurve,
        preventDuplicates: true,
      ),
    ];
  }

  // Premium navigation with optimized performance
  static Future<T?> navigateTo<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    return Get.toNamed(
          routeName,
          arguments: arguments,
          id: id,
          preventDuplicates: preventDuplicates,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Premium replace navigation with smooth transitions
  static Future<T?> navigateOff<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    return Get.offNamed(
          routeName,
          arguments: arguments,
          id: id,
          preventDuplicates: preventDuplicates,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Premium navigation with preloading
  static Future<T?> navigateToWithPreload<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    bool preventDuplicates = true,
    Map<String, String>? parameters,
    VoidCallback? onPreload,
  }) async {
    // Execute preload callback if provided
    onPreload?.call();

    return Get.toNamed(
          routeName,
          arguments: arguments,
          id: id,
          preventDuplicates: preventDuplicates,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Fast navigation for frequently used routes
  static Future<T?> fastNavigateTo<T>(String routeName, {dynamic arguments}) {
    return Get.toNamed(
          routeName,
          arguments: arguments,
          preventDuplicates: true,
        ) ??
        Future.value(null);
  }

  // Replace all screens with named route
  static Future<T?> navigateOffAll<T>(
    String routeName, {
    dynamic arguments,
    int? id,
    Map<String, String>? parameters,
  }) {
    return Get.offAllNamed(
          routeName,
          arguments: arguments,
          id: id,
          parameters: parameters,
        ) ??
        Future.value(null);
  }

  // Navigate back
  static void navigateBack<T>({T? result}) {
    if (Get.currentRoute != splash && Navigator.canPop(Get.context!)) {
      Get.back(result: result);
    }
  }
}
