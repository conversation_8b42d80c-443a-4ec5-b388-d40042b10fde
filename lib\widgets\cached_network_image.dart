import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:shimmer/shimmer.dart';

// Premium cache manager with optimized settings
final CacheManager _premiumCacheManager = CacheManager(
  Config(
    'premium_images_cache',
    stalePeriod:
        const Duration(days: 30), // Longer cache for premium experience
    maxNrOfCacheObjects: 2000, // Increased cache size for better performance
    repo: JsonCacheInfoRepository(databaseName: 'premium_cache.db'),
    fileService: HttpFileService(),
  ),
);

// Premium cached network image with instant loading
CachedNetworkImage cachedNetworkImage(
  String image, {
  bool forceRefresh = false,
  BoxFit fit = BoxFit.cover,
  BorderRadius? borderRadius,
  double? width,
  double? height,
}) {
  return CachedNetworkImage(
    imageUrl: image,
    cacheManager: _premiumCacheManager,
    width: width,
    height: height,
    // Force refresh if needed (useful when categories change)
    cacheKey: forceRefresh
        ? '${image}_${DateTime.now().millisecondsSinceEpoch}'
        : null,
    // Optimized memory cache settings
    memCacheWidth: width?.toInt(),
    memCacheHeight: height?.toInt(),
    maxWidthDiskCache: 1000,
    maxHeightDiskCache: 1000,
    // Premium error handling with better UX
    errorWidget: (context, url, error) => _buildErrorWidget(borderRadius),
    // Minimal loading indicator for premium feel
    progressIndicatorBuilder: (context, url, downloadProgress) =>
        _buildLoadingWidget(borderRadius),
    // Optimized image builder
    imageBuilder: (context, imageProvider) => Container(
      decoration: BoxDecoration(
        borderRadius:
            borderRadius ?? const BorderRadius.all(Radius.circular(14)),
        image: DecorationImage(
          image: imageProvider,
          fit: fit,
        ),
      ),
    ),
    // Premium fade-in animation
    fadeInDuration: const Duration(milliseconds: 200),
    fadeOutDuration: const Duration(milliseconds: 100),
  );
}

// Premium loading widget with subtle shimmer
Widget _buildLoadingWidget(BorderRadius? borderRadius) {
  return Shimmer.fromColors(
    baseColor: Colors.grey.withValues(alpha: 0.08),
    highlightColor: Colors.grey.withValues(alpha: 0.04),
    period: const Duration(milliseconds: 1200),
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius:
            borderRadius ?? const BorderRadius.all(Radius.circular(14)),
      ),
    ),
  );
}

// Premium error widget with better UX
Widget _buildErrorWidget(BorderRadius? borderRadius) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.grey.withValues(alpha: 0.05),
      borderRadius: borderRadius ?? const BorderRadius.all(Radius.circular(14)),
    ),
    child: const Center(
      child: Icon(
        Icons.image_outlined,
        color: Colors.grey,
        size: 24,
      ),
    ),
  );
}

// Preload image for instant display
Future<void> preloadImage(String imageUrl) async {
  try {
    await _premiumCacheManager.getSingleFile(imageUrl);
  } catch (e) {
    debugPrint('Error preloading image: $e');
  }
}
