import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';

/// Premium performance monitoring service for optimal app performance
class PerformanceMonitor extends GetxService {
  static PerformanceMonitor get instance => Get.find<PerformanceMonitor>();
  
  Timer? _performanceTimer;
  final List<double> _frameRates = [];
  final List<Duration> _navigationTimes = [];
  final Map<String, DateTime> _screenStartTimes = {};
  
  final RxDouble _currentFPS = 60.0.obs;
  final RxDouble _averageFPS = 60.0.obs;
  final RxBool _isPerformanceGood = true.obs;
  
  // Performance thresholds
  static const double _minAcceptableFPS = 45.0;
  static const double _targetFPS = 60.0;
  static const int _maxNavigationTimeMs = 500;
  
  double get currentFPS => _currentFPS.value;
  double get averageFPS => _averageFPS.value;
  bool get isPerformanceGood => _isPerformanceGood.value;
  
  @override
  void onInit() {
    super.onInit();
    _startPerformanceMonitoring();
    _setupFrameRateMonitoring();
  }
  
  @override
  void onClose() {
    _performanceTimer?.cancel();
    super.onClose();
  }
  
  /// Start comprehensive performance monitoring
  void _startPerformanceMonitoring() {
    _performanceTimer = Timer.periodic(
      const Duration(seconds: 5),
      (_) => _analyzePerformance(),
    );
  }
  
  /// Setup frame rate monitoring
  void _setupFrameRateMonitoring() {
    if (kDebugMode) {
      SchedulerBinding.instance.addTimingsCallback(_onFrameRender);
    }
  }
  
  /// Handle frame render timing
  void _onFrameRender(List<FrameTiming> timings) {
    if (timings.isEmpty) return;
    
    try {
      final frameTime = timings.last.totalSpan.inMicroseconds / 1000.0;
      final fps = 1000.0 / frameTime;
      
      _currentFPS.value = fps.clamp(0.0, _targetFPS);
      _frameRates.add(_currentFPS.value);
      
      // Keep only last 100 frame rates for average calculation
      if (_frameRates.length > 100) {
        _frameRates.removeAt(0);
      }
      
      _calculateAverageFPS();
    } catch (e) {
      debugPrint('Error in frame rate monitoring: $e');
    }
  }
  
  /// Calculate average FPS
  void _calculateAverageFPS() {
    if (_frameRates.isEmpty) return;
    
    final sum = _frameRates.reduce((a, b) => a + b);
    _averageFPS.value = sum / _frameRates.length;
    
    // Update performance status
    _isPerformanceGood.value = _averageFPS.value >= _minAcceptableFPS;
  }
  
  /// Analyze overall performance
  void _analyzePerformance() {
    try {
      final stats = getPerformanceStats();
      
      // Log performance if below threshold
      if (_averageFPS.value < _minAcceptableFPS) {
        debugPrint('Performance Warning: Low FPS detected - ${_averageFPS.value.toStringAsFixed(1)}');
        _suggestOptimizations();
      }
      
      // Log navigation performance
      if (_navigationTimes.isNotEmpty) {
        final avgNavTime = _getAverageNavigationTime();
        if (avgNavTime.inMilliseconds > _maxNavigationTimeMs) {
          debugPrint('Navigation Warning: Slow navigation detected - ${avgNavTime.inMilliseconds}ms');
        }
      }
    } catch (e) {
      debugPrint('Error analyzing performance: $e');
    }
  }
  
  /// Suggest performance optimizations
  void _suggestOptimizations() {
    debugPrint('Performance Suggestions:');
    debugPrint('- Clear image cache if memory usage is high');
    debugPrint('- Reduce animation complexity');
    debugPrint('- Check for memory leaks in controllers');
    debugPrint('- Consider lazy loading for heavy widgets');
  }
  
  /// Track screen navigation start
  void trackScreenStart(String screenName) {
    _screenStartTimes[screenName] = DateTime.now();
  }
  
  /// Track screen navigation end
  void trackScreenEnd(String screenName) {
    final startTime = _screenStartTimes[screenName];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      _navigationTimes.add(duration);
      
      // Keep only last 50 navigation times
      if (_navigationTimes.length > 50) {
        _navigationTimes.removeAt(0);
      }
      
      _screenStartTimes.remove(screenName);
      
      debugPrint('Navigation to $screenName took ${duration.inMilliseconds}ms');
    }
  }
  
  /// Get average navigation time
  Duration _getAverageNavigationTime() {
    if (_navigationTimes.isEmpty) return Duration.zero;
    
    final totalMs = _navigationTimes
        .map((d) => d.inMilliseconds)
        .reduce((a, b) => a + b);
    
    return Duration(milliseconds: totalMs ~/ _navigationTimes.length);
  }
  
  /// Track custom performance metric
  void trackCustomMetric(String name, double value) {
    debugPrint('Custom Metric - $name: $value');
  }
  
  /// Track app startup time
  void trackAppStartup(Duration startupTime) {
    debugPrint('App startup took ${startupTime.inMilliseconds}ms');
    
    if (startupTime.inMilliseconds > 3000) {
      debugPrint('Startup Warning: App took longer than 3 seconds to start');
    }
  }
  
  /// Get comprehensive performance statistics
  Map<String, dynamic> getPerformanceStats() {
    return {
      'currentFPS': _currentFPS.value,
      'averageFPS': _averageFPS.value,
      'isPerformanceGood': _isPerformanceGood.value,
      'frameRateHistory': List.from(_frameRates),
      'averageNavigationTime': _getAverageNavigationTime().inMilliseconds,
      'navigationHistory': _navigationTimes.map((d) => d.inMilliseconds).toList(),
      'platform': Platform.operatingSystem,
      'isDebugMode': kDebugMode,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// Export performance data for analysis
  String exportPerformanceData() {
    final stats = getPerformanceStats();
    return '''
Performance Report
==================
Current FPS: ${stats['currentFPS'].toStringAsFixed(1)}
Average FPS: ${stats['averageFPS'].toStringAsFixed(1)}
Performance Status: ${stats['isPerformanceGood'] ? 'Good' : 'Poor'}
Average Navigation Time: ${stats['averageNavigationTime']}ms
Platform: ${stats['platform']}
Generated: ${stats['timestamp']}

Frame Rate History (last 10):
${_frameRates.take(10).map((fps) => fps.toStringAsFixed(1)).join(', ')}

Navigation Times (last 10):
${_navigationTimes.take(10).map((d) => '${d.inMilliseconds}ms').join(', ')}
''';
  }
  
  /// Reset performance metrics
  void resetMetrics() {
    _frameRates.clear();
    _navigationTimes.clear();
    _screenStartTimes.clear();
    _currentFPS.value = _targetFPS;
    _averageFPS.value = _targetFPS;
    _isPerformanceGood.value = true;
    debugPrint('Performance metrics reset');
  }
  
  /// Check if device is performing well
  bool isDevicePerformingWell() {
    return _averageFPS.value >= _minAcceptableFPS && 
           _getAverageNavigationTime().inMilliseconds <= _maxNavigationTimeMs;
  }
  
  /// Get performance grade (A-F)
  String getPerformanceGrade() {
    if (_averageFPS.value >= 55) return 'A';
    if (_averageFPS.value >= 50) return 'B';
    if (_averageFPS.value >= 45) return 'C';
    if (_averageFPS.value >= 35) return 'D';
    return 'F';
  }
}
