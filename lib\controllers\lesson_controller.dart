import 'dart:io';

import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/quiz_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/quiz_model.dart';
import '../res/style.dart';
import '../services/db_service.dart';
import '../services/memory_manager.dart';
import '../services/shared_preferences.dart';

class LessonController extends GetxController {
  final Map<String, Future<File?>> _imageCache = {};

  RxList<dynamic> mergedItems = <dynamic>[].obs;
  RxList<dynamic> allItems = <dynamic>[].obs;

  // Track category changes to force image refresh
  RxBool categoriesChanged = false.obs;

  int homeItemsPerPage = 10; // Increased from 3 to ensure content is visible
  RxInt currentPageForHomeItems = 1.obs;
  RxList<dynamic> homeDisplayedItems = <dynamic>[].obs;
  int libraryItemsPerPage = 10; // Increased from 3 to ensure content is visible
  RxInt currentPageForLibraryItems = 1.obs;
  RxList<dynamic> libraryDisplayedItems = <dynamic>[].obs;

  DocumentSnapshot? _lastDoc;
  bool _hasMoreData = true;
  final int _chunkSize = 10;

  final DatabaseHelper dbHelper = DatabaseHelper();
  RxList<LessonModel> allLessons = <LessonModel>[].obs;
  // Premium cache manager for superior performance
  final CacheManager _cacheManager = CacheManager(
    Config(
      'premium_lessons_cache',
      stalePeriod:
          const Duration(days: 30), // Longer cache for premium experience
      maxNrOfCacheObjects: 3000, // Increased cache size for lessons
      repo: JsonCacheInfoRepository(databaseName: 'lessons_cache.db'),
      fileService: HttpFileService(),
    ),
  );

  CacheManager get cacheManager => _cacheManager;

  @override
  void onInit() {
    super.onInit();
    // Load initial content immediately to prevent empty state
    _loadInitialContentSync();
  }

  // Load initial content synchronously for immediate display
  void _loadInitialContentSync() {
    // Load content immediately without Future.microtask delay
    _loadLocalContentFirst().then((_) {
      debugPrint('Local content loaded, starting network load...');
      _loadNetworkContentInBackground();
    }).catchError((e) {
      debugPrint('Error loading initial content: $e');
      // If local loading fails, still try network
      _loadNetworkContentInBackground();
    });
  }

  // Load local content first for immediate display
  Future<void> _loadLocalContentFirst() async {
    try {
      debugPrint('=== Starting _loadLocalContentFirst ===');

      // Load lessons from local database
      final localLessons = await dbHelper.getLessons();
      debugPrint('Database returned ${localLessons.length} lessons');
      if (localLessons.isNotEmpty) {
        allLessons.assignAll(localLessons);
        debugPrint('Assigned ${localLessons.length} lessons to allLessons');
      } else {
        debugPrint('No lessons found in local database');
      }

      // Load quizzes from local database (check if QuizController is available)
      QuizController? quizController;
      if (Get.isRegistered<QuizController>()) {
        quizController = Get.find<QuizController>();
        final localQuizzes = await dbHelper.getQuizzes();
        if (localQuizzes.isNotEmpty) {
          quizController.allQuizzes.assignAll(localQuizzes);
          debugPrint(
              'Loaded ${localQuizzes.length} quizzes from local database');
        }

        final localShuffleQuizzes = await dbHelper.getShuffleQuizzes();
        if (localShuffleQuizzes.isNotEmpty) {
          quizController.allShuffleQuizzes.assignAll(localShuffleQuizzes);
          debugPrint(
              'Loaded ${localShuffleQuizzes.length} shuffle quizzes from local database');
        }
      } else {
        debugPrint(
            'QuizController not yet registered, will load quizzes later');
      }

      // Merge and display content immediately if we have local data
      final hasLessons = allLessons.isNotEmpty;
      final hasQuizzes =
          quizController != null && quizController.allQuizzes.isNotEmpty;
      final hasShuffleQuizzes =
          quizController != null && quizController.allShuffleQuizzes.isNotEmpty;

      debugPrint(
          'Content check: lessons=$hasLessons, quizzes=$hasQuizzes, shuffleQuizzes=$hasShuffleQuizzes');

      if (hasLessons || hasQuizzes || hasShuffleQuizzes) {
        debugPrint('Found local data, merging and shuffling...');
        mergeAndShuffleItems(
            isShuffle: true, shouldClear: true, from: 'local_load');
        shuffleAllItems(isShuffle: true, from: 'local_load', shouldClear: true);
        debugPrint('Content merged and displayed from local data');
        debugCurrentState();
      } else {
        // If no local data, force a network load immediately
        debugPrint('No local data found, forcing immediate network load...');
        _loadNetworkContentInBackground();
      }
    } catch (e) {
      debugPrint('Error loading local content: $e');
    }
  }

  // Load network content in background
  void _loadNetworkContentInBackground() {
    Future.microtask(() async {
      try {
        debugPrint('Starting network content load...');
        await loadContent();
        debugPrint('Network content loaded successfully');
        debugCurrentState();
      } catch (e) {
        debugPrint('Error loading network content in background: $e');
      }
    });
  }

  // Premium aggressive image preloading for instant display
  Future<void> startAggressiveImagePreloading() async {
    try {
      // Load from local database first for immediate display
      final localLessons = await dbHelper.getLessons();
      if (localLessons.isNotEmpty) {
        allLessons.assignAll(localLessons);

        // Start premium preloading strategy
        await _premiumImagePreloadingStrategy();
      }
    } catch (e) {
      debugPrint('Error in premium image preloading: $e');
    }
  }

  // Premium image preloading strategy for instant loading
  Future<void> _premiumImagePreloadingStrategy() async {
    try {
      // Phase 1: Preload critical images (first 10 lessons) immediately
      final criticalLessons = allLessons.take(10).toList();
      await _preloadCriticalImages(criticalLessons);

      // Phase 2: Preload remaining images in background with priority
      _preloadRemainingImagesWithPriority();
    } catch (e) {
      debugPrint('Error in premium preloading strategy: $e');
    }
  }

  // Preload remaining images with intelligent priority
  void _preloadRemainingImagesWithPriority() {
    Future.microtask(() async {
      try {
        final remainingLessons = allLessons.skip(10).toList();

        // Sort by priority (lesson number for sequential learning)
        remainingLessons
            .sort((a, b) => (a.lessonNo ?? 0).compareTo(b.lessonNo ?? 0));

        for (var lesson in remainingLessons) {
          for (var page in lesson.pages ?? []) {
            if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
              try {
                await _cacheManager.getSingleFile(page.pagePhotoLink!);
                // Small delay to prevent overwhelming the system
                await Future.delayed(const Duration(milliseconds: 10));
              } catch (e) {
                debugPrint('Error preloading background image: $e');
              }
            }
          }
        }
        debugPrint('Background image preloading completed');
      } catch (e) {
        debugPrint('Error in background preloading: $e');
      }
    });
  }

  // Preload critical images for instant display
  Future<void> _preloadCriticalImages(List<LessonModel> lessons) async {
    final futures = <Future>[];

    for (var lesson in lessons) {
      for (var page in lesson.pages ?? []) {
        if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
          futures.add(_cacheManager.getSingleFile(page.pagePhotoLink!));
        }
      }
    }

    // Preload critical images in parallel with controlled concurrency
    const batchSize = 5;
    for (int i = 0; i < futures.length; i += batchSize) {
      final batch = futures.skip(i).take(batchSize);
      try {
        await Future.wait(batch, eagerError: false);
      } catch (e) {
        debugPrint('Error in critical image batch: $e');
      }
    }
  }

  Future<void> preloadInitialImages() async {
    debugPrint('Preloading images for initial screen');
    // Reduce the number of items to preload for faster startup
    final priorityItems =
        mergedItems.take(3).toList(); // Reduced from homeItemsPerPage

    // Use sequential loading instead of parallel to reduce system load
    for (var item in priorityItems) {
      try {
        if (item is LessonModel) {
          // Only preload the first page image to reduce load
          final firstPage =
              item.pages?.isNotEmpty == true ? item.pages!.first : null;
          if (firstPage?.pagePhotoLink != null &&
              firstPage!.pagePhotoLink!.isNotEmpty) {
            await fetchAndCacheImage(
              firstPage.pagePhotoLink!,
              '${item.lessonId}_${firstPage.pageNo}.jpg',
            );
          }
        } else if (item is QuizModel) {
          if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
            await fetchAndCacheImage(
              item.quizImageLink!,
              '${item.quizId}_image.jpg',
            );
          }
        } else if (item is ShuffleQuizModel) {
          // Only preload first question image
          final firstQuestion = item.questionsList?.isNotEmpty == true
              ? item.questionsList!.first
              : null;
          if (firstQuestion?.qsImage != null &&
              firstQuestion!.qsImage!.isNotEmpty) {
            await fetchAndCacheImage(
              firstQuestion.qsImage!,
              '${item.quizId}_${firstQuestion.qsNo}.jpg',
            );
          }
        }

        // Small delay to prevent overwhelming the system
        await Future.delayed(const Duration(milliseconds: 50));
      } catch (e) {
        debugPrint('Error preloading image: $e');
      }
    }

    // Preload remaining images in background
    final remainingItems = mergedItems.skip(homeItemsPerPage).toList();
    for (var item in remainingItems) {
      if (item is LessonModel) {
        for (var page in item.pages ?? []) {
          if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
            fetchAndCacheImage(
              page.pagePhotoLink!,
              '${item.lessonId}_${page.pageNo}.jpg',
            ).catchError((e) {
              debugPrint('Error preloading background image: $e');
              return null;
            });
          }
        }
      } else if (item is QuizModel) {
        if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
          fetchAndCacheImage(
            item.quizImageLink!,
            '${item.quizId}_image.jpg',
          ).catchError((e) {
            debugPrint('Error preloading background image: $e');
            return null;
          });
        }
      } else if (item is ShuffleQuizModel) {
        for (var question in item.questionsList ?? []) {
          if (question.qsImage != null && question.qsImage!.isNotEmpty) {
            fetchAndCacheImage(
              question.qsImage!,
              '${item.quizId}_${question.qsNo}.jpg',
            ).catchError((e) {
              debugPrint('Error preloading background image: $e');
              return null;
            });
          }
        }
      }
    }
  }

  Future<void> prefetchImages(List<String> urls, String prefix) async {
    final futures = urls.asMap().entries.map((entry) {
      final url = entry.value;
      final fileName = '$prefix${entry.key}.jpg';
      debugPrint('Prefetching image: $url');
      return fetchAndCacheImage(url, fileName).then((file) {
        debugPrint('Prefetched $url');
        return file;
      }).catchError((e) {
        debugPrint('Error prefetching $url: $e');
        return null;
      });
    }).toList();
    await Future.wait(futures);
  }

  Future<File?> fetchAndCacheImage(String imageUrl, String fileName,
      {bool? isnew}) async {
    final key = '$imageUrl$fileName';
    // Force refresh if categories changed or explicitly requested
    final shouldRefresh = isnew == true || categoriesChanged.value;

    if (!_imageCache.containsKey(key) || shouldRefresh) {
      if (shouldRefresh) {
        debugPrint('Force refreshing image: $imageUrl');
        // Remove from cache manager to force fresh download
        await _cacheManager.removeFile(key);
      }

      _imageCache[key] = _cacheManager.getSingleFile(imageUrl).catchError((e) {
        debugPrint('Error fetching image $imageUrl: $e');
        throw e; // Re-throw to maintain proper error handling
      });
    }
    return _imageCache[key];
  }

  void clearImageCache() {
    debugPrint('Clearing all image caches...');
    _imageCache.clear();
    _cacheManager.emptyCache();

    // Force clear all cached network images globally
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();

    // Use memory manager for optimal cleanup
    if (Get.isRegistered<MemoryManager>()) {
      MemoryManager.instance.performManualCleanup();
      MemoryManager.instance.optimizeImageCache();
    }

    // Mark that categories have changed to force image refresh
    categoriesChanged.value = true;

    debugPrint('Image cache cleared successfully with memory optimization');
  }

  void markCategoriesRefreshed() {
    categoriesChanged.value = false;
  }

  // Debug method to check current state
  void debugCurrentState() {
    debugPrint('=== LessonController Debug State ===');
    debugPrint('allLessons: ${allLessons.length}');
    debugPrint('mergedItems: ${mergedItems.length}');
    debugPrint('allItems: ${allItems.length}');
    debugPrint('homeDisplayedItems: ${homeDisplayedItems.length}');
    debugPrint('libraryDisplayedItems: ${libraryDisplayedItems.length}');
    debugPrint('=====================================');
  }

  List<dynamic> reorderItems(List<dynamic> items) {
    List<dynamic> articles = items.whereType<LessonModel>().toList();
    List<dynamic> shuffleQuizzes = items.whereType<ShuffleQuizModel>().toList();
    List<dynamic> normalQuizzes = items.whereType<QuizModel>().toList();

    List<dynamic> reorderedItems = [];
    int articleIndex = 0, shuffleIndex = 0, normalIndex = 0;

    while (articleIndex < articles.length ||
        shuffleIndex < shuffleQuizzes.length ||
        normalIndex < normalQuizzes.length) {
      for (int i = 0; i < 3 && articleIndex < articles.length; i++) {
        reorderedItems.add(articles[articleIndex++]);
      }
      if (shuffleIndex < shuffleQuizzes.length) {
        reorderedItems.add(shuffleQuizzes[shuffleIndex++]);
      }
      if (normalIndex < normalQuizzes.length) {
        reorderedItems.add(normalQuizzes[normalIndex++]);
      }
    }
    return reorderedItems;
  }

  void loadInitialMergedItems() {
    debugPrint(
        'loadInitialMergedItems: clearing homeDisplayedItems (was ${homeDisplayedItems.length})');
    homeDisplayedItems.clear();
    final initialChunk = mergedItems.take(homeItemsPerPage).toList();
    homeDisplayedItems.addAll(initialChunk);
    currentPageForHomeItems.value = 1;
    debugPrint(
        'loadInitialMergedItems: loaded ${homeDisplayedItems.length} items');
  }

  void loadInitialAllItems() {
    debugPrint(
        'loadInitialAllItems: clearing libraryDisplayedItems (was ${libraryDisplayedItems.length})');
    libraryDisplayedItems.clear();
    final initialChunk = allItems.take(libraryItemsPerPage).toList();
    libraryDisplayedItems.addAll(initialChunk);
    currentPageForLibraryItems.value = 1;
    debugPrint(
        'loadInitialAllItems: loaded ${libraryDisplayedItems.length} items');
  }

  void loadMoreHomeDisplayItems() {
    final startIndex = homeDisplayedItems.length;
    if (startIndex >= mergedItems.length) return;
    final nextChunk =
        mergedItems.skip(startIndex).take(homeItemsPerPage).toList();
    homeDisplayedItems.addAll(nextChunk);
    currentPageForHomeItems.value++;
  }

  void loadMoreLibraryDisplayItems() {
    final startIndex = libraryDisplayedItems.length;
    if (startIndex >= allItems.length) return;
    final nextChunk =
        allItems.skip(startIndex).take(libraryItemsPerPage).toList();
    libraryDisplayedItems.addAll(nextChunk);
    currentPageForLibraryItems.value++;
  }

  loadContent() async {
    try {
      final QuizController quizController = Get.find();

      // Load all content in parallel
      await Future.wait([
        loadLessons(),
        quizController.loadQuizzes(),
        quizController.loadShuffleQuizzes(),
      ]);

      // Always merge and shuffle after loading content
      mergeAndShuffleItems(isShuffle: true, shouldClear: true, from: 'load');
      shuffleAllItems(isShuffle: true, from: 'load', shouldClear: true);

      debugPrint('Content loaded and merged successfully');
    } catch (e) {
      debugPrint('Error in loadContent: $e');
    }
  }

  void mergeAndShuffleItems({
    required bool isShuffle,
    required bool shouldClear,
    required String from,
    List<ShuffleQuizModel>? fetchedShuffleQuizzes,
    List<QuizModel>? fetchedQuizzes,
    List<LessonModel>? fetchedLessons,
  }) {
    if (!Get.isRegistered<ProfileController>()) return;
    final QuizController quizController = Get.find();

    debugPrint(
        'mergeAndShuffleItems called from: $from, shouldClear: $shouldClear');

    List<LessonModel> filteredLessons = _filterItemsByCategory(allLessons);
    List<ShuffleQuizModel> filteredShuffleQuizzes =
        _filterItemsByCategory(quizController.allShuffleQuizzes);
    List<QuizModel> filteredQuizzes =
        _filterItemsByCategory(quizController.allQuizzes);

    if (shouldClear) {
      debugPrint('Clearing mergedItems (was ${mergedItems.length} items)');
      mergedItems.clear();
    }

    if (fetchedShuffleQuizzes != null) {
      for (var fetchedQuiz in fetchedShuffleQuizzes) {
        mergedItems.removeWhere((item) =>
            item is ShuffleQuizModel && item.quizId == fetchedQuiz.quizId);
        mergedItems.add(fetchedQuiz);
      }
    }
    if (fetchedQuizzes != null) {
      for (var fetchedQuiz in fetchedQuizzes) {
        mergedItems.removeWhere(
            (item) => item is QuizModel && item.quizId == fetchedQuiz.quizId);
        mergedItems.add(fetchedQuiz);
      }
    }
    if (fetchedLessons != null) {
      for (var fetchedLesson in fetchedLessons) {
        mergedItems.removeWhere((item) =>
            item is LessonModel && item.lessonId == fetchedLesson.lessonId);
        mergedItems.add(fetchedLesson);
      }
    }

    for (var lesson in filteredLessons) {
      if (!mergedItems.any(
          (item) => item is LessonModel && item.lessonId == lesson.lessonId)) {
        mergedItems.add(lesson);
      }
    }
    for (var shuffleQuiz in filteredShuffleQuizzes) {
      if (!mergedItems.any((item) =>
          item is ShuffleQuizModel && item.quizId == shuffleQuiz.quizId)) {
        mergedItems.add(shuffleQuiz);
      }
    }
    for (var quiz in filteredQuizzes) {
      if (!mergedItems
          .any((item) => item is QuizModel && item.quizId == quiz.quizId)) {
        mergedItems.add(quiz);
      }
    }

    mergedItems.assignAll(reorderItems(mergedItems));
    if (isShuffle) mergedItems.shuffle();
    debugPrint('Final mergedItems count: ${mergedItems.length}');
    loadInitialMergedItems();

    // Mark categories as refreshed after loading new content
    if (shouldClear && from == 'sheet') {
      Future.delayed(const Duration(milliseconds: 500), () {
        markCategoriesRefreshed();
      });
    }
  }

  void shuffleAllItems({
    required bool isShuffle,
    required String from,
    required bool shouldClear,
    List<ShuffleQuizModel>? fetchedShuffleQuizzes,
    List<QuizModel>? fetchedQuizzes,
    List<LessonModel>? fetchedLessons,
  }) {
    if (!Get.isRegistered<ProfileController>()) return;
    final QuizController quizController = Get.find();

    debugPrint('shuffleAllItems called from: $from, shouldClear: $shouldClear');

    List<LessonModel> filteredLessons =
        _filterLibraryItemsByCategory(allLessons);
    List<ShuffleQuizModel> filteredShuffleQuizzes =
        _filterLibraryItemsByCategory(quizController.allShuffleQuizzes);
    List<QuizModel> filteredQuizzes =
        _filterLibraryItemsByCategory(quizController.allQuizzes);

    if (shouldClear) {
      debugPrint('Clearing allItems (was ${allItems.length} items)');
      allItems.clear();
    }

    if (fetchedShuffleQuizzes != null) {
      for (var fetchedQuiz in fetchedShuffleQuizzes) {
        allItems.removeWhere((item) =>
            item is ShuffleQuizModel && item.quizId == fetchedQuiz.quizId);
        allItems.add(fetchedQuiz);
      }
    }
    if (fetchedQuizzes != null) {
      for (var fetchedQuiz in fetchedQuizzes) {
        allItems.removeWhere(
            (item) => item is QuizModel && item.quizId == fetchedQuiz.quizId);
        allItems.add(fetchedQuiz);
      }
    }
    if (fetchedLessons != null) {
      for (var fetchedLesson in fetchedLessons) {
        allItems.removeWhere((item) =>
            item is LessonModel && item.lessonId == fetchedLesson.lessonId);
        allItems.add(fetchedLesson);
      }
    }

    for (var lesson in filteredLessons) {
      if (!allItems.any(
          (item) => item is LessonModel && item.lessonId == lesson.lessonId)) {
        allItems.add(lesson);
      }
    }
    for (var shuffleQuiz in filteredShuffleQuizzes) {
      if (!allItems.any((item) =>
          item is ShuffleQuizModel && item.quizId == shuffleQuiz.quizId)) {
        allItems.add(shuffleQuiz);
      }
    }
    for (var quiz in filteredQuizzes) {
      if (!allItems
          .any((item) => item is QuizModel && item.quizId == quiz.quizId)) {
        allItems.add(quiz);
      }
    }

    allItems.assignAll(reorderItems(allItems));
    if (isShuffle) allItems.shuffle();
    debugPrint('Final allItems count: ${allItems.length}');
    loadInitialAllItems();

    // Mark categories as refreshed after loading new content
    if (shouldClear && from == 'sheet') {
      Future.delayed(const Duration(milliseconds: 500), () {
        markCategoriesRefreshed();
      });
    }
  }

  List<T> _filterItemsByCategory<T>(List<T> items) {
    if (!Get.isRegistered<ProfileController>()) return items;
    ProfileController profileController = Get.find();
    final favCategories = profileController.userr.value.listOfFavCategories;
    if (favCategories == null || favCategories.isEmpty) return items;

    debugPrint('Filtering items by categories: $favCategories');
    final filteredItems = items.where((item) {
      String? category;
      if (item is LessonModel) {
        category = item.category;
      } else if (item is ShuffleQuizModel) {
        category = item.category;
      } else if (item is QuizModel) {
        category = item.category;
      } else {
        return false;
      }
      return favCategories.contains(category);
    }).toList();
    debugPrint(
        'Filtered ${items.length} items to ${filteredItems.length} items');
    return filteredItems;
  }

  List<T> _filterLibraryItemsByCategory<T>(List<T> items) {
    if (!Get.isRegistered<ProfileController>()) return items;
    ProfileController profileController = Get.find();
    final favCategories = profileController.userr.value.listOfLibraryCategories;
    if (favCategories == null || favCategories.isEmpty) return items;

    debugPrint('Filtering library items by categories: $favCategories');
    final filteredItems = items.where((item) {
      String? category;
      if (item is LessonModel) {
        category = item.category;
      } else if (item is ShuffleQuizModel) {
        category = item.category;
      } else if (item is QuizModel) {
        category = item.category;
      } else {
        return false;
      }
      return favCategories.contains(category);
    }).toList();
    debugPrint(
        'Filtered library ${items.length} items to ${filteredItems.length} items');
    return filteredItems;
  }

  Future<void> loadLessons() async {
    try {
      final lessons = await dbHelper.getLessons();
      if (lessons.isNotEmpty) {
        allLessons.assignAll(lessons);
      }
      bool shouldUpdate = await shouldFetchFromFirestore();
      if (shouldUpdate) {
        await fetchLessonsFromFirestore();
      }
    } catch (e) {
      debugPrint('Error loading lessons: $e');
    }
  }

  Future<bool> shouldFetchFromFirestore() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTime') ?? 0;
    try {
      final latestFirestoreSnapshot = await firestore
          .collection('lessons')
          .orderBy('lastUpdated', descending: true)
          .limit(1)
          .get();
      if (latestFirestoreSnapshot.docs.isNotEmpty) {
        final firestoreLatestTimestamp =
            (latestFirestoreSnapshot.docs.first['lastUpdated'] as Timestamp)
                .millisecondsSinceEpoch;
        return firestoreLatestTimestamp > lastSyncTime;
      }
      return true;
    } catch (e) {
      return true;
    }
  }

  Future<void> fetchLessonsFromFirestore() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTime') ?? 0;
    try {
      _hasMoreData = true;
      _lastDoc = null;
      while (_hasMoreData) {
        await _fetchOneChunk(lastSyncTime);
      }
      await SharedPrefs.setData(
        key: 'lastSyncTime',
        value: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      debugPrint('Error fetching lessons: $e');
    }
  }

  Future<void> _fetchOneChunk(int lastSyncTime) async {
    Query query = firestore
        .collection('lessons')
        .where('lastUpdated',
            isGreaterThan: Timestamp.fromMillisecondsSinceEpoch(lastSyncTime))
        .orderBy('lastUpdated', descending: false)
        .limit(_chunkSize);

    if (_lastDoc != null) {
      query = query.startAfterDocument(_lastDoc!);
    }

    final snapshot = await query.get();
    if (snapshot.docs.isEmpty) {
      _hasMoreData = false;
      return;
    }

    List<LessonModel> fetchedLessons = [];
    for (var doc in snapshot.docs) {
      try {
        final lesson = LessonModel.fromJson(doc.data() as Map<String, dynamic>);
        fetchedLessons.add(lesson);
        await dbHelper.insertLesson(lesson);
        allLessons.add(lesson);
      } catch (err) {
        debugPrint('Error processing lesson: $err');
      }
    }

    _lastDoc = snapshot.docs.last;
    mergeAndShuffleItems(
        fetchedLessons: fetchedLessons,
        isShuffle: false,
        from: 'lesson chunk',
        shouldClear: false);
    shuffleAllItems(
        fetchedLessons: fetchedLessons,
        isShuffle: false,
        from: 'lesson chunk shuffle all',
        shouldClear: false);

    if (snapshot.docs.length < _chunkSize) {
      _hasMoreData = false;
    }
  }
}
