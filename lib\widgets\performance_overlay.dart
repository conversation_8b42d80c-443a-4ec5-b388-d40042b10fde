import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/memory_manager.dart';
import '../services/performance_monitor.dart';

/// Premium performance overlay for development monitoring
class PerformanceOverlay extends StatelessWidget {
  final Widget child;
  final bool showInProduction;

  const PerformanceOverlay({
    super.key,
    required this.child,
    this.showInProduction = false,
  });

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode unless explicitly enabled for production
    if (!kDebugMode && !showInProduction) {
      return child;
    }

    return Stack(
      children: [
        child,
        if (Get.isRegistered<PerformanceMonitor>() && Get.isRegistered<MemoryManager>())
          const Positioned(
            top: 50,
            right: 10,
            child: PerformanceWidget(),
          ),
      ],
    );
  }
}

/// Performance monitoring widget
class PerformanceWidget extends StatelessWidget {
  const PerformanceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // FPS Monitor
          Obx(() {
            final monitor = PerformanceMonitor.instance;
            final fps = monitor.currentFPS;
            final color = _getFPSColor(fps);
            
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.speed, color: color, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${fps.toStringAsFixed(1)} FPS',
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          }),
          
          const SizedBox(height: 4),
          
          // Memory Monitor
          Obx(() {
            final memoryManager = MemoryManager.instance;
            final isLowMemory = memoryManager.isLowMemory;
            final color = isLowMemory ? Colors.red : Colors.green;
            
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.memory, color: color, size: 16),
                const SizedBox(width: 4),
                Text(
                  isLowMemory ? 'LOW MEM' : 'MEM OK',
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          }),
          
          const SizedBox(height: 4),
          
          // Performance Grade
          Obx(() {
            final monitor = PerformanceMonitor.instance;
            final grade = monitor.getPerformanceGrade();
            final color = _getGradeColor(grade);
            
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.grade, color: color, size: 16),
                const SizedBox(width: 4),
                Text(
                  'Grade $grade',
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Color _getFPSColor(double fps) {
    if (fps >= 55) return Colors.green;
    if (fps >= 45) return Colors.yellow;
    return Colors.red;
  }

  Color _getGradeColor(String grade) {
    switch (grade) {
      case 'A':
        return Colors.green;
      case 'B':
        return Colors.lightGreen;
      case 'C':
        return Colors.yellow;
      case 'D':
        return Colors.orange;
      case 'F':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// Performance debug panel for detailed monitoring
class PerformanceDebugPanel extends StatelessWidget {
  const PerformanceDebugPanel({super.key});

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Performance Debug Panel',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          // Performance Stats
          if (Get.isRegistered<PerformanceMonitor>())
            Obx(() {
              final stats = PerformanceMonitor.instance.getPerformanceStats();
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatRow('Current FPS', '${stats['currentFPS'].toStringAsFixed(1)}'),
                  _buildStatRow('Average FPS', '${stats['averageFPS'].toStringAsFixed(1)}'),
                  _buildStatRow('Performance', stats['isPerformanceGood'] ? 'Good' : 'Poor'),
                  _buildStatRow('Avg Navigation', '${stats['averageNavigationTime']}ms'),
                ],
              );
            }),
          
          const SizedBox(height: 12),
          
          // Memory Stats
          if (Get.isRegistered<MemoryManager>())
            Obx(() {
              final stats = MemoryManager.instance.getMemoryStats();
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatRow('Memory Usage', '${stats['currentUsage'].toStringAsFixed(1)}MB'),
                  _buildStatRow('Low Memory', stats['isLowMemory'] ? 'Yes' : 'No'),
                  _buildStatRow('Image Cache', '${stats['imageCacheSize']} items'),
                ],
              );
            }),
          
          const SizedBox(height: 12),
          
          // Action Buttons
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton(
                onPressed: () {
                  if (Get.isRegistered<MemoryManager>()) {
                    MemoryManager.instance.performManualCleanup();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Cache'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  if (Get.isRegistered<PerformanceMonitor>()) {
                    final report = PerformanceMonitor.instance.exportPerformanceData();
                    debugPrint(report);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Export Report'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          Text(
            value,
            style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
