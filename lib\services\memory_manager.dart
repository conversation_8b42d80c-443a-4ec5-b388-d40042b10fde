import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Premium memory management service for optimal app performance
class MemoryManager extends GetxService {
  static MemoryManager get instance => Get.find<MemoryManager>();

  Timer? _memoryMonitorTimer;
  final RxDouble _memoryUsage = 0.0.obs;
  final RxBool _isLowMemory = false.obs;

  // Memory thresholds (in MB)
  static const double _warningThreshold = 150.0;
  static const double _criticalThreshold = 200.0;

  double get memoryUsage => _memoryUsage.value;
  bool get isLowMemory => _isLowMemory.value;

  @override
  void onInit() {
    super.onInit();
    _startMemoryMonitoring();
    _setupMemoryWarningListener();
  }

  @override
  void onClose() {
    _memoryMonitorTimer?.cancel();
    super.onClose();
  }

  /// Start monitoring memory usage
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _checkMemoryUsage(),
    );
  }

  /// Setup memory warning listener for iOS/Android
  void _setupMemoryWarningListener() {
    if (Platform.isIOS || Platform.isAndroid) {
      SystemChannels.lifecycle.setMessageHandler((message) async {
        if (message == AppLifecycleState.paused.toString()) {
          await _performMemoryCleanup();
        }
        return null;
      });
    }
  }

  /// Check current memory usage
  Future<void> _checkMemoryUsage() async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // Get memory info from platform
        final memoryInfo = await _getMemoryInfo();
        _memoryUsage.value = memoryInfo;

        // Check if memory usage is high
        if (memoryInfo > _criticalThreshold) {
          _isLowMemory.value = true;
          await _performAggressiveCleanup();
        } else if (memoryInfo > _warningThreshold) {
          _isLowMemory.value = true;
          await _performLightCleanup();
        } else {
          _isLowMemory.value = false;
        }
      }
    } catch (e) {
      debugPrint('Error checking memory usage: $e');
    }
  }

  /// Get memory information from platform
  Future<double> _getMemoryInfo() async {
    try {
      // This is a simplified version - in production you might use
      // platform-specific methods to get actual memory usage
      return 100.0; // Placeholder value
    } catch (e) {
      debugPrint('Error getting memory info: $e');
      return 0.0;
    }
  }

  /// Perform light memory cleanup
  Future<void> _performLightCleanup() async {
    try {
      // Clear image cache partially
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Force garbage collection
      await _forceGarbageCollection();

      debugPrint('Light memory cleanup performed');
    } catch (e) {
      debugPrint('Error in light cleanup: $e');
    }
  }

  /// Perform aggressive memory cleanup
  Future<void> _performAggressiveCleanup() async {
    try {
      // Clear all image caches
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Clear network image cache
      await _clearNetworkImageCache();

      // Force garbage collection multiple times
      for (int i = 0; i < 3; i++) {
        await _forceGarbageCollection();
        await Future.delayed(const Duration(milliseconds: 100));
      }

      debugPrint('Aggressive memory cleanup performed');
    } catch (e) {
      debugPrint('Error in aggressive cleanup: $e');
    }
  }

  /// Perform memory cleanup when app goes to background
  Future<void> _performMemoryCleanup() async {
    try {
      // Clear temporary caches
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Clear unused controllers
      await _cleanupUnusedControllers();

      // Force garbage collection
      await _forceGarbageCollection();

      debugPrint('Background memory cleanup performed');
    } catch (e) {
      debugPrint('Error in background cleanup: $e');
    }
  }

  /// Clear network image cache
  Future<void> _clearNetworkImageCache() async {
    try {
      // Clear cached network images
      PaintingBinding.instance.imageCache.clear();
    } catch (e) {
      debugPrint('Error clearing network image cache: $e');
    }
  }

  /// Cleanup unused controllers
  Future<void> _cleanupUnusedControllers() async {
    try {
      // This would cleanup any temporary controllers
      // Implementation depends on your specific controller usage
      debugPrint('Unused controllers cleaned up');
    } catch (e) {
      debugPrint('Error cleaning up controllers: $e');
    }
  }

  /// Force garbage collection
  Future<void> _forceGarbageCollection() async {
    try {
      // Force garbage collection
      await Future.delayed(const Duration(milliseconds: 1));
    } catch (e) {
      debugPrint('Error forcing garbage collection: $e');
    }
  }

  /// Optimize image cache settings based on device capabilities
  void optimizeImageCache() {
    try {
      final imageCache = PaintingBinding.instance.imageCache;

      // Set optimal cache size based on available memory
      if (_memoryUsage.value < _warningThreshold) {
        imageCache.maximumSize = 1000; // High capacity
        imageCache.maximumSizeBytes = 100 << 20; // 100MB
      } else {
        imageCache.maximumSize = 500; // Reduced capacity
        imageCache.maximumSizeBytes = 50 << 20; // 50MB
      }

      debugPrint('Image cache optimized for current memory usage');
    } catch (e) {
      debugPrint('Error optimizing image cache: $e');
    }
  }

  /// Manual memory cleanup trigger
  Future<void> performManualCleanup() async {
    await _performLightCleanup();
    optimizeImageCache();
  }

  /// Get memory usage statistics
  Map<String, dynamic> getMemoryStats() {
    return {
      'currentUsage': _memoryUsage.value,
      'isLowMemory': _isLowMemory.value,
      'warningThreshold': _warningThreshold,
      'criticalThreshold': _criticalThreshold,
      'imageCacheSize': PaintingBinding.instance.imageCache.currentSize,
      'imageCacheBytes': PaintingBinding.instance.imageCache.currentSizeBytes,
    };
  }
}
