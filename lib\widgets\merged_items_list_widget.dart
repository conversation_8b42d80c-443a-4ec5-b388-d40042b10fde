import 'dart:io';
import 'dart:ui';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'interests_widget.dart';

class MergedItemsList extends StatefulWidget {
  final bool isForLibrary;
  final ScrollController scrollController;

  const MergedItemsList({
    Key? key,
    required this.isForLibrary,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<MergedItemsList> createState() => _MergedItemsListState();
}

class _MergedItemsListState extends State<MergedItemsList>
    with TickerProviderStateMixin {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  final Set<int> _loadedAdIndices = {};
  final Map<int, BannerAd> _bannerAds = {};
  final Map<int, AnimationController> _itemAnimations = {};
  final Map<int, AnimationController> _adAnimations = {};

  bool _showScrollToTopButton = false;
  bool _isLoadingMore = false;
  double _lastScrollOffset = 0;
  late AnimationController _fabAnimationController;
  late Animation<double> _fabScaleAnimation;

  // Enhanced preloading
  static const int _preloadAheadCount = 10;
  static const int _adPreloadAheadCount = 3;
  final Set<int> _preloadedIndices = {};

  // Ad units map
  final Map<int, String> _adUnitMap = Platform.isAndroid
      ? {
          3: 'ca-app-pub-8639821055582439/**********',
          6: 'ca-app-pub-8639821055582439/**********',
          9: 'ca-app-pub-8639821055582439/**********',
          -1: 'ca-app-pub-8639821055582439/**********',
        }
      : {
          3: 'ca-app-pub-8639821055582439/3769550858',
          6: 'ca-app-pub-8639821055582439/4265224933',
          9: 'ca-app-pub-8639821055582439/1746888714',
          -1: 'ca-app-pub-8639821055582439/2021354915',
        };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    widget.scrollController.addListener(_onScroll);

    if (!(profileController.userr.value.isPremiumUser ?? false)) {
      _preloadAds();
    }

    // Aggressive preloading
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadNextItems();
      _preloadImages();
    });
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabScaleAnimation = CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    );
  }

  void _preloadAds() {
    // Preload multiple ads ahead
    for (int i = 3; i <= 18; i += 3) {
      _createBannerAd(i);
    }
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    _fabAnimationController.dispose();

    for (var controller in _itemAnimations.values) {
      controller.dispose();
    }
    for (var controller in _adAnimations.values) {
      controller.dispose();
    }
    for (var ad in _bannerAds.values) {
      ad.dispose();
    }

    _itemAnimations.clear();
    _adAnimations.clear();
    _bannerAds.clear();
    super.dispose();
  }

  void _onScroll() {
    if (!mounted) return;

    final currentOffset = widget.scrollController.position.pixels;
    final maxExtent = widget.scrollController.position.maxScrollExtent;
    final direction = currentOffset > _lastScrollOffset
        ? ScrollDirection.reverse
        : ScrollDirection.forward;

    // Smooth FAB animation
    final shouldShowButton = currentOffset > AppConstants.minScrollForButton;
    if (shouldShowButton != _showScrollToTopButton) {
      setState(() => _showScrollToTopButton = shouldShowButton);
      if (shouldShowButton) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }

    // Load more items with better prediction
    if (!_isLoadingMore &&
        currentOffset >= maxExtent - AppConstants.infiniteScrollOffset * 2) {
      _loadMoreItems();
    }

    // Preload images based on scroll direction
    if (direction == ScrollDirection.reverse && !_isLoadingMore) {
      _preloadImages();
    }

    _lastScrollOffset = currentOffset;
  }

  void _scrollToTop() {
    widget.scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 800),
      curve: Curves.fastOutSlowIn,
    );
  }

  Future<void> _preloadImages() async {
    final currentIndex = _getCurrentVisibleIndex();
    final itemsToDisplay = widget.isForLibrary
        ? lessonController.libraryDisplayedItems
        : lessonController.homeDisplayedItems;

    // Preload images for next items
    for (int i = currentIndex;
        i < currentIndex + _preloadAheadCount && i < itemsToDisplay.length;
        i++) {
      if (_preloadedIndices.contains(i)) continue;
      _preloadedIndices.add(i);

      final item = itemsToDisplay[i];
      Future.microtask(() async {
        await _preloadItemImages(item);
      });
    }
  }

  Future<void> _preloadItemImages(dynamic item) async {
    try {
      if (item is LessonModel) {
        for (var page in item.pages ?? []) {
          if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
            await lessonController.cacheManager
                .getSingleFile(page.pagePhotoLink!);
          }
        }
      } else if (item is QuizModel) {
        if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
          await lessonController.cacheManager
              .getSingleFile(item.quizImageLink!);
        }
      } else if (item is ShuffleQuizModel) {
        for (var question in item.questionsList ?? []) {
          if (question.qsImage != null && question.qsImage!.isNotEmpty) {
            await lessonController.cacheManager
                .getSingleFile(question.qsImage!);
          }
        }
      }
    } catch (e) {
      debugPrint('Error preloading images: $e');
    }
  }

  int _getCurrentVisibleIndex() {
    if (!widget.scrollController.hasClients) return 0;
    final offset = widget.scrollController.offset;
    const itemHeight = 220.0; // Approximate height
    return (offset / itemHeight).floor();
  }

  void _preloadNextItems() {
    final currentLength = widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;

    final totalItems = widget.isForLibrary
        ? lessonController.allItems.length
        : lessonController.mergedItems.length;

    if (currentLength < totalItems) {
      // Load more items proactively
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && !_isLoadingMore) {
          _loadMoreItems();
        }
      });
    }
  }

  void _loadMoreItems() async {
    if (_isLoadingMore) return;
    setState(() => _isLoadingMore = true);

    // Load items
    if (widget.isForLibrary) {
      lessonController.loadMoreLibraryDisplayItems();
    } else {
      lessonController.loadMoreHomeDisplayItems();
    }

    // Preload ads for upcoming positions
    if (!(profileController.userr.value.isPremiumUser ?? false)) {
      final currentLength = widget.isForLibrary
          ? lessonController.libraryDisplayedItems.length
          : lessonController.homeDisplayedItems.length;

      for (int i = 0; i < _adPreloadAheadCount; i++) {
        final nextAdIndex = currentLength + 3 + (i * 3);
        if (!_bannerAds.containsKey(nextAdIndex)) {
          _createBannerAd(nextAdIndex);
        }
      }
    }

    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      setState(() => _isLoadingMore = false);
      _preloadImages();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Obx(() {
          final isPremiumUser =
              profileController.userr.value.isPremiumUser ?? false;
          final itemsToDisplay = widget.isForLibrary
              ? lessonController.libraryDisplayedItems
              : lessonController.homeDisplayedItems;

          if (itemsToDisplay.isEmpty) {
            return _buildEmptyState();
          }

          return CustomScrollView(
            controller: widget.scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            slivers: [
              // Interests header
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    interestsWidget(context, widget.isForLibrary),
                    const SizedBox(height: 20),
                  ],
                ),
              ),

              // Main content list
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    if (!isPremiumUser && _isAdIndex(index)) {
                      return _buildAnimatedAd(index);
                    }

                    final itemIndex = _calculateItemIndex(index, isPremiumUser);
                    if (itemIndex < 0 || itemIndex >= itemsToDisplay.length) {
                      return const SizedBox.shrink();
                    }

                    final item = itemsToDisplay[itemIndex];
                    return _buildAnimatedItem(item, itemIndex, index);
                  },
                  childCount: itemsToDisplay.length +
                      (isPremiumUser ? 0 : _countAds(itemsToDisplay.length)),
                ),
              ),

              // Loading indicator at bottom
              if (_isLoadingMore)
                const SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(mainColor),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                ),

              // Bottom padding
              const SliverPadding(padding: EdgeInsets.only(bottom: 50)),
            ],
          );
        }),

        // Floating Action Button with animation
        if (_showScrollToTopButton)
          Positioned(
            bottom: 20,
            right: 20,
            child: ScaleTransition(
              scale: _fabScaleAnimation,
              child: FloatingActionButton(
                onPressed: _scrollToTop,
                backgroundColor: Colors.transparent,
                elevation: 8,
                child: Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: mainColorsGradient,
                    boxShadow: [
                      BoxShadow(
                        color: mainColor.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.keyboard_arrow_up_rounded,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return ListView(
      physics: const BouncingScrollPhysics(),
      children: [
        interestsWidget(context, widget.isForLibrary),
        const SizedBox(height: 100),
        Center(
          child: Column(
            children: [
              Icon(
                Icons.explore_outlined,
                size: 80,
                color: Colors.grey[300],
              ),
              const SizedBox(height: 20),
              Text(
                'Učitavanje sadržaja...',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedItem(dynamic item, int itemIndex, int visualIndex) {
    // Create animation controller if not exists
    if (!_itemAnimations.containsKey(visualIndex)) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (visualIndex % 3) * 100),
        vsync: this,
      );
      _itemAnimations[visualIndex] = controller;
      controller.forward();
    }

    final animationController = _itemAnimations[visualIndex]!;

    // Safety check to prevent accessing disposed controllers
    if (!mounted) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
        child: _buildItemWidget(item),
      );
    }

    final animation = CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOutCubic,
    );

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        try {
          // Clamp animation value to prevent opacity assertion errors
          final clampedValue = animation.value.clamp(0.0, 1.0);

          return Transform.translate(
            offset: Offset(0, (1 - clampedValue) * 30),
            child: Opacity(
              opacity: clampedValue,
              child: Transform.scale(
                scale: 0.95 + (0.05 * clampedValue),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
                  child: _buildItemWidget(item),
                ),
              ),
            ),
          );
        } catch (e) {
          // Fallback to non-animated widget if animation fails
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
            child: _buildItemWidget(item),
          );
        }
      },
    );
  }

  Widget _buildAnimatedAd(int index) {
    // Create animation controller for ad if not exists
    if (!_adAnimations.containsKey(index)) {
      final controller = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );
      _adAnimations[index] = controller;

      // Start animation when ad is loaded
      if (_loadedAdIndices.contains(index)) {
        controller.forward();
      }
    }

    final animationController = _adAnimations[index]!;

    // Safety check to prevent accessing disposed controllers
    if (!mounted) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 10, 16, 20),
        child: _buildAdWidget(index),
      );
    }

    final animation = CurvedAnimation(
      parent: animationController,
      curve: Curves.easeOutBack,
    );

    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        try {
          // Clamp animation value to prevent opacity assertion errors
          final clampedValue = animation.value.clamp(0.0, 1.0);

          return Transform.scale(
            scale: 0.9 + (0.1 * clampedValue),
            child: Opacity(
              opacity: clampedValue,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 10, 16, 20),
                child: _buildAdWidget(index),
              ),
            ),
          );
        } catch (e) {
          // Fallback to non-animated widget if animation fails
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 10, 16, 20),
            child: _buildAdWidget(index),
          );
        }
      },
    );
  }

  bool _isAdIndex(int i) {
    return i > 0 && (i + 1) % 4 == 0;
  }

  int _countAds(int itemCount) {
    int adCount = 0;
    for (int i = 0; i < itemCount + adCount; i++) {
      if (_isAdIndex(i)) adCount++;
    }
    return adCount;
  }

  int _calculateItemIndex(int visualIndex, bool isPremiumUser) {
    if (isPremiumUser) return visualIndex;

    int itemIndex = 0;
    for (int i = 0; i <= visualIndex; i++) {
      if (!_isAdIndex(i)) {
        if (i == visualIndex) return itemIndex;
        itemIndex++;
      }
    }
    return itemIndex;
  }

  void _createBannerAd(int index) {
    if (_bannerAds.containsKey(index)) return;

    final adUnitId = _adUnitMap[index] ?? _adUnitMap[-1]!;

    final BannerAd banner = BannerAd(
      adUnitId: adUnitId,
      size: AdSize(
        width: (Get.width - AppConstants.adWidthMargin).toInt(),
        height: AppConstants.adHeight.toInt(),
      ),
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (!mounted) {
            ad.dispose();
            return;
          }
          setState(() {
            _loadedAdIndices.add(index);
          });

          // Trigger ad animation
          if (_adAnimations.containsKey(index)) {
            _adAnimations[index]!.forward();
          }
        },
        onAdFailedToLoad: (ad, error) {
          ad.dispose();
          if (mounted) {
            setState(() {
              _bannerAds.remove(index);
              _loadedAdIndices.remove(index);
            });
          }
        },
      ),
    );

    _bannerAds[index] = banner;
    banner.load();
  }

  Widget _buildAdWidget(int index) {
    final isAdLoaded =
        _bannerAds.containsKey(index) && _loadedAdIndices.contains(index);

    if (!isAdLoaded && !_bannerAds.containsKey(index)) {
      _createBannerAd(index);
    }

    return Container(
      width: Get.width - AppConstants.adWidthMargin,
      height: AppConstants.adHeight + 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8, bottom: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'REKLAMA',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: isAdLoaded
                  ? AdWidget(ad: _bannerAds[index]!)
                  : Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.grey[200]!,
                            Colors.grey[100]!,
                            Colors.grey[200]!,
                          ],
                          stops: const [0.0, 0.5, 1.0],
                          begin: const Alignment(-1.0, 0.0),
                          end: const Alignment(1.0, 0.0),
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(mainColor),
                              strokeWidth: 2,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Učitavanje oglasa...',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemWidget(dynamic item) {
    if (item is LessonModel) {
      return BoxWidget(lesson: item);
    } else if (item is QuizModel) {
      return BoxWidget(quiz: item);
    } else if (item is ShuffleQuizModel) {
      return BoxWidget(shuffleQuiz: item);
    }
    return const SizedBox.shrink();
  }
}

class AppConstants {
  static const double adHeight = 300;
  static const double adWidthMargin = 50;
  static const double minScrollForButton = 300;
  static const double infiniteScrollOffset = 400;
}
