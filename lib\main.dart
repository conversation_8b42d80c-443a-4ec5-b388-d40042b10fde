import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/heart_controller.dart' show HeartController;
import 'package:bibl/controllers/leaderboard_controller.dart';
import 'package:bibl/routes/app_routes.dart';
import 'package:bibl/services/ad_mob_service.dart';
import 'package:bibl/services/memory_manager.dart';
import 'package:bibl/services/performance_monitor.dart';
import 'package:figma_to_flutter/figma_to_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'controllers/audio_controller.dart';
import 'controllers/category_controller.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';
import 'res/style.dart';
import 'services/notification_service.dart';
import 'package:timezone/data/latest_all.dart' as tz;

import 'services/shared_preferences.dart';

// Define a background message handler for handling notifications when the app is in the background or terminated
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // print("Handling a background message: ${message.messageId}");
}

// Preload fonts to prevent text jumping with better performance
Future<void> _preloadFonts() async {
  try {
    // Use more efficient font preloading
    const textStyle = TextStyle(fontFamily: 'Poppins');
    final textPainter = TextPainter(
      text: const TextSpan(text: 'Loading...', style: textStyle),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    debugPrint('Fonts preloaded successfully');
  } catch (e) {
    debugPrint('Error preloading fonts: $e');
  }
}

// Initialize critical services in parallel for faster startup
Future<void> _initializeCriticalServices() async {
  try {
    // Run critical initializations in parallel with proper typing
    await Future.wait<void>([
      SharedPrefs.sharedPreferencesInitialization(),
      Firebase.initializeApp().then((_) {}),
      MobileAds.instance.initialize().then((_) {}),
    ]);
    debugPrint('Critical services initialized');
  } catch (e) {
    debugPrint('Error initializing critical services: $e');
  }
}

// Initialize controllers with optimized sequence
void _initializeControllers() {
  try {
    // Initialize performance monitoring first
    Get.put(PerformanceMonitor(), permanent: true);

    // Initialize memory manager for optimal performance
    Get.put(MemoryManager(), permanent: true);

    // Initialize controllers in dependency order for better performance
    Get.put(HeartController(), permanent: true);
    Get.put(CategoryController(), permanent: true);
    Get.put(AudioController(), permanent: true);
    Get.put(AuthController(), permanent: true);
    Get.put(QuizController(), permanent: true);
    Get.put(LessonController(), permanent: true);
    Get.put(RewardedAdManagerController(), permanent: true);
    Get.put(LeaderboardController(), permanent: true);

    // Optimize image cache after controllers are initialized
    MemoryManager.instance.optimizeImageCache();

    debugPrint(
        'Controllers initialized with performance monitoring and memory optimization');
  } catch (e) {
    debugPrint('Error initializing controllers: $e');
  }
}

// Initialize background operations without blocking UI
void _initializeBackgroundOperations() {
  // Start all heavy operations in background with better error handling
  Future.microtask(() async {
    try {
      // Wait a bit for controllers to be ready
      await Future.delayed(const Duration(milliseconds: 100));

      // Start image preloading after categories are loaded
      final lessonController = Get.find<LessonController>();
      final quizController = Get.find<QuizController>();

      // Start preloading immediately with cached data in parallel
      await Future.wait([
        lessonController.startAggressiveImagePreloading(),
        quizController.startAggressiveImagePreloading(),
      ]);
      debugPrint('Background image preloading completed');
    } catch (e) {
      debugPrint('Error in background operations: $e');
    }
  });
}

// void getToken() async {
//   FirebaseMessaging messaging = FirebaseMessaging.instance;

//   try {
//     String? token = await messaging.getToken();
//     if (token != null) {
//       print("FCM Token: $token");
//       // You can use the token here, for example, show it in a toast
//     } else {
//       print("Failed to fetch FCM Token");
//     }
//   } catch (e) {
//     print("Error fetching FCM Token: $e");
//   }
// }

Future<void> main() async {
  // Track app startup time for performance monitoring
  final startupStartTime = DateTime.now();

  // Load environment variables first
  await dotenv.load(fileName: ".env");
  WidgetsFlutterBinding.ensureInitialized();

  // Run font preloading and critical services in parallel for faster startup
  await Future.wait([
    _preloadFonts(),
    _initializeCriticalServices(),
  ]);

  // Initialize controllers with optimized sequence
  _initializeControllers();

  // Track startup completion
  final startupDuration = DateTime.now().difference(startupStartTime);

  // Move heavy operations to background to not block UI
  _initializeBackgroundOperations();

  // Log startup performance
  if (Get.isRegistered<PerformanceMonitor>()) {
    PerformanceMonitor.instance.trackAppStartup(startupDuration);
  }
  // Initialize Firebase Messaging

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Set up foreground notification display
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    if (message.notification != null) {
      // Use NotificationService to display notifications in the foreground if needed
      NotificationService().showNotification(
        title: message.notification!.title ?? '',
        body: message.notification!.body ?? '',
      );
    }
  });
  // getToken();
  NotificationService().initNotification();
  tz.initializeTimeZones();

  // Initialize other dependencies
  Figma.setup(deviceWidth: 375, deviceHeight: 812);

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.dark,
    statusBarIconBrightness: Brightness.dark,
  ));

  // runApp(DevicePreview(enabled: !kReleaseMode, builder: (context) => MyApp()));
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // Professional navigation configuration
      defaultTransition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: myCustomPrimarySwatch,
        fontFamily: 'Poppins',
        // Ensure consistent text rendering
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: 'Poppins'),
          bodyMedium: TextStyle(fontFamily: 'Poppins'),
          bodySmall: TextStyle(fontFamily: 'Poppins'),
          displayLarge: TextStyle(fontFamily: 'Poppins'),
          displayMedium: TextStyle(fontFamily: 'Poppins'),
          displaySmall: TextStyle(fontFamily: 'Poppins'),
          headlineLarge: TextStyle(fontFamily: 'Poppins'),
          headlineMedium: TextStyle(fontFamily: 'Poppins'),
          headlineSmall: TextStyle(fontFamily: 'Poppins'),
          titleLarge: TextStyle(fontFamily: 'Poppins'),
          titleMedium: TextStyle(fontFamily: 'Poppins'),
          titleSmall: TextStyle(fontFamily: 'Poppins'),
          labelLarge: TextStyle(fontFamily: 'Poppins'),
          labelMedium: TextStyle(fontFamily: 'Poppins'),
          labelSmall: TextStyle(fontFamily: 'Poppins'),
        ),
        // Ensure consistent input decoration
        inputDecorationTheme: const InputDecorationTheme(
          hintStyle: TextStyle(fontFamily: 'Poppins'),
          labelStyle: TextStyle(fontFamily: 'Poppins'),
        ),
      ),
      // Use professional route management
      initialRoute: AppRoutes.splash,
      getPages: AppRoutes.getPages(),
    );
  }
}
